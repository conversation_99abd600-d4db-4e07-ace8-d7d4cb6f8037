/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

/**
 * InterChat Donation System Library
 * 
 * This library provides a comprehensive donation management system with Ko-fi integration,
 * premium features, and donor perk management. It follows InterChat's architectural patterns
 * for maintainability and performance.
 */

// Core Components
export { DonationManager } from './core/DonationManager.js';
export { PremiumService } from './core/PremiumService.js';

// Services
export { KofiWebhookService } from './services/KofiWebhookService.js';
export { DonorPerkService } from './services/DonorPerkService.js';

// Schemas
export * from './schemas/kofi.js';
export * from './schemas/donation.js';

// Types
export * from './types/DonationTypes.js';
export * from './types/PremiumTypes.js';

// Utils
export * from './utils/currency.js';
export * from './utils/validation.js';
export * from './utils/constants.js';

// Scripts (for internal use)
export { seedDonorPerks } from './scripts/seedDonorPerks.js';
