import { CacheManager } from '#src/managers/CacheManager.js';
import { DonationManager } from '#src/managers/DonationManager.js';
import Logger from '#src/utils/Logger.js';

/**
 * Service for managing premium features and Ko-fi Supporter tier verification
 * Integrates with existing DonationManager for premium status checks
 */
export class PremiumService {
  private readonly donationManager: DonationManager;
  private readonly cacheManager: CacheManager;

  // Cache TTL for premium status checks (5 minutes)
  private readonly PREMIUM_CACHE_TTL = 5 * 60 * 1000;

  constructor(donationManager: DonationManager, cacheManager: CacheManager) {
    this.donationManager = donationManager;
    this.cacheManager = cacheManager;
  }

  /**
   * Check if user has Ko-fi Supporter tier access (premium hub features)
   * Uses existing DonationManager.hasMediaPremium() as Ko-fi Supporter tier grants media premium
   */
  async hasSupporterTier(userId: string): Promise<boolean> {
    const cacheKey = `premium:supporter:${userId}`;

    try {
      // Check cache first
      const cached = await this.cacheManager.get(cacheKey);
      if (cached !== null) {
        return cached as boolean;
      }

      // Check premium status using DonationManager
      // Ko-fi Supporter tier grants media premium, so we can use this check
      const hasSupporter = await this.donationManager.hasMediaPremium(userId);

      // Cache the result
      await this.cacheManager.set(cacheKey, hasSupporter, this.PREMIUM_CACHE_TTL);

      Logger.debug(`Premium status check for user ${userId}: ${hasSupporter}`);
      return hasSupporter;
    }
    catch (error) {
      Logger.error(`Failed to check premium status for user ${userId}`, error);
      // Return false on error to fail safely
      return false;
    }
  }

  /**
   * Check if user can use premium hub features (hub name customization)
   * Alias for hasSupporterTier for semantic clarity
   */
  async canCustomizeHubName(userId: string): Promise<boolean> {
    return this.hasSupporterTier(userId);
  }

  /**
   * Invalidate premium status cache for a user
   * Useful when premium status changes (e.g., after Ko-fi webhook)
   */
  async invalidatePremiumCache(userId: string): Promise<void> {
    const cacheKey = `premium:supporter:${userId}`;
    await this.cacheManager.delete(cacheKey);
    Logger.debug(`Invalidated premium cache for user ${userId}`);
  }

  /**
   * Get premium feature status summary for a user
   */
  async getPremiumStatus(userId: string): Promise<{
    hasSupporter: boolean;
    canCustomizeHubName: boolean;
    hasMediaPremium: boolean;
  }> {
    const [hasSupporter, hasMediaPremium] = await Promise.all([
      this.hasSupporterTier(userId),
      this.donationManager.hasMediaPremium(userId),
    ]);

    return {
      hasSupporter,
      canCustomizeHubName: hasSupporter,
      hasMediaPremium,
    };
  }

  /**
   * Log premium feature usage for analytics
   */
  logPremiumFeatureUsage(userId: string, feature: string, hubId?: string): void {
    const context = hubId ? { userId, feature, hubId } : { userId, feature };
    Logger.info(`Premium feature used: ${feature}`, context);
  }
}
