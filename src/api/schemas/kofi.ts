import Logger from '#src/utils/Logger.js';
import { z } from 'zod';

/**
 * Schema for validating Ko-fi webhook payloads
 * Based on Ko-fi webhook documentation and real payload examples
 */
export const kofiPayloadSchema = z.object({
  verification_token: z.string().uuid('Verification token must be a valid UUID'),
  message_id: z.string().uuid('Message ID must be a valid UUID'),
  timestamp: z.string().datetime('Timestamp must be a valid ISO-8601 datetime'),
  type: z.enum(['Donation', 'Subscription', 'Commission', 'Shop Order']),
  is_public: z.boolean(),
  from_name: z.string().min(1, 'From name cannot be empty'),
  message: z.string().optional(),
  amount: z.string().regex(/^\d+\.\d{2}$/, 'Amount must be in decimal format (e.g., "3.00")'),
  url: z.string().url('URL must be a valid URL').optional(),
  email: z.string().email('Email must be valid').optional(),
  currency: z.string().length(3, 'Currency must be a 3-letter code'),
  is_subscription_payment: z.boolean().optional().default(false),
  is_first_subscription_payment: z.boolean().optional().default(false),
  kofi_transaction_id: z.string().uuid('Transaction ID must be a valid UUID'),
  shop_items: z.array(z.any()).nullable().optional(),
  tier_name: z.string().nullable().optional(),
  shipping: z
    .object({
      full_name: z.string(),
      street_address: z.string(),
      city: z.string(),
      state_or_province: z.string(),
      postal_code: z.string(),
      country: z.string(),
      country_code: z.string(),
      telephone: z.string().optional(),
    })
    .nullable()
    .optional(),
});

export type KofiPayload = z.infer<typeof kofiPayloadSchema>;

/**
 * Schema for the Ko-fi webhook request body
 * Ko-fi sends data as form-encoded with a 'data' field containing JSON
 */
export const kofiWebhookSchema = z.object({
  data: z.string().transform((str, ctx) => {
    try {
      const parsed = JSON.parse(str);
      const result = kofiPayloadSchema.safeParse(parsed);
      if (!result.success) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Invalid Ko-fi payload structure',
          path: ['data'],
        });
        return z.NEVER;
      }
      return result.data;
    }
    catch {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Invalid JSON in data field',
        path: ['data'],
      });
      return z.NEVER;
    }
  }),
});

export type KofiWebhookPayload = z.infer<typeof kofiWebhookSchema>;

/**
 * Convert a Ko-fi payload to our internal donation format
 */
export const toDonationData = (payload: KofiPayload) => ({
  kofiTransactionId: payload.kofi_transaction_id,
  messageId: payload.message_id,
  amount: parseFloat(payload.amount),
  currency: payload.currency,
  fromName: payload.from_name,
  message: payload.message || null,
  email: payload.email || null,
  isPublic: payload.is_public,
  kofiTimestamp: new Date(payload.timestamp),
  kofiUrl: payload.url || null,
});

/**
 * Currency conversion rates (simplified - in production, use a real API)
 * This is a fallback for basic conversion to USD
 */
export const CURRENCY_TO_USD_RATES: Record<string, number> = {
  USD: 1.0,
  EUR: 1.1,
  GBP: 1.25,
  CAD: 0.75,
  AUD: 0.65,
  JPY: 0.007,
  // Add more currencies as needed
};

/**
 * Convert amount to USD for consistent storage
 */
export const convertToUSD = (amount: number, currency: string): number => {
  const rate = CURRENCY_TO_USD_RATES[currency.toUpperCase()];
  if (!rate) {
    // If currency not supported, assume 1:1 with USD
    Logger.warn(`Unsupported currency: ${currency}, assuming 1:1 with USD`);
    return amount;
  }
  return Math.round(amount * rate * 100) / 100; // Round to 2 decimal places
};
